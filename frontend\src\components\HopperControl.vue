<template>
  <div class="control-card">
    <!-- Card Header -->
    <div class="card-header">
      <h3 class="card-title">Hopper {{ drumId }}</h3>
      <div class="status-indicator">
        <div :class="['status-dot', isBladeRunning ? 'status-running' : 'status-stopped']"></div>
        <span class="status-text">{{ isBladeRunning ? 'Running' : 'Stopped' }}</span>
      </div>
    </div>

    <div class="card-content">
      <!-- Blade Screws Status -->
      <div class="info-grid">
        <div v-for="screw in bladeScrews" :key="screw.id" class="info-box">
          <label class="info-label">Screw {{ screw.id }} Position</label>
          <div class="info-value">{{ formatPosition(screw.position) }} µm</div>
        </div>
      </div>

      <!-- Collective Blade Motion -->
      <div class="control-group">
        <h4 class="group-title">Collective Blade Motion</h4>
        <div class="parameter-grid">
          <div class="form-group">
            <label class="form-label">Mode</label>
            <select v-model="collectiveMotion.mode" class="form-select" :disabled="!connected || isBladeRunning">
              <option value="relative">Relative</option>
              <option value="absolute">Absolute</option>
              <option value="homing">Homing</option>
            </select>
          </div>
          <div v-if="collectiveMotion.mode !== 'homing'" class="form-group">
            <label class="form-label">Distance (µm)</label>
            <input v-model.number="collectiveMotion.distance" type="number" step="100" class="form-input" :disabled="!connected || isBladeRunning"/>
          </div>
        </div>
        <div class="button-row">
          <button @click="startCollectiveMotion" :disabled="!connected || isBladeRunning" class="btn btn-primary">Start Motion</button>
          <button @click="cancelCollectiveMotion" :disabled="!connected || !isBladeRunning" class="btn btn-danger">Cancel Motion</button>
        </div>
      </div>

      <!-- Individual Screw Control (FIXED LAYOUT) -->
      <div class="control-group">
        <h4 class="group-title">Individual Screw Control</h4>
        <!-- This is now a flex column to stack the controls vertically -->
        <div class="individual-screw-container">
          <div v-for="screwId in [0, 1]" :key="screwId" class="pressure-box bg-gray-50 border-gray-200">
            <h5 class="group-title text-gray-800">Screw {{ screwId }}</h5>
            <div class="pressure-set">
              <input v-model.number="individualMotion[screwId].distance" type="number" step="100" class="form-input-sm" :disabled="!connected || getScrewRunning(screwId)" placeholder="Distance (µm)"/>
              <button @click="startIndividualMotion(screwId)" :disabled="!connected || getScrewRunning(screwId)" class="btn-set bg-green-500 hover:bg-green-600 disabled:bg-gray-300">Move</button>
              <button @click="cancelIndividualMotion(screwId)" :disabled="!connected || !getScrewRunning(screwId)" class="btn-set bg-red-500 hover:bg-red-600 disabled:bg-gray-300">Stop</button>
            </div>
          </div>
        </div>
      </div>

      <!-- Error Message -->
      <div v-if="errorMessage" class="error-message mt-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded">
        {{ errorMessage }}
      </div>
    </div>
  </div>
</template>

<script>
// The <script> block is unchanged as the logic is correct.
import { ref, computed } from 'vue';
import apiService from '@/services/api';

export default {
  name: 'HopperControl',
  props: {
    drumId: { type: Number, required: true },
    bladeScrews: { type: Array, default: () => [] },
    connected: { type: Boolean, default: false }
  },
  emits: ['error', 'success', 'motion-started', 'motion-cancelled'],
  setup(props, { emit }) {
    const collectiveMotion = ref({ mode: 'relative', distance: 1000.0 });
    const individualMotion = ref({ 0: { distance: 500.0 }, 1: { distance: 500.0 } });
    const errorMessage = ref('');
    const errorTimeout = ref(null);

    const isBladeRunning = computed(() => props.bladeScrews?.some(s => s.running));
    const getScrewRunning = (id) => props.bladeScrews?.find(s => s.id === id)?.running;

    const showError = (error) => {
      const message = error?.response?.data?.detail || error?.message || 'An error occurred';
      errorMessage.value = message;
      if (errorTimeout.value) {
        clearTimeout(errorTimeout.value);
      }
      errorTimeout.value = setTimeout(() => {
        errorMessage.value = '';
      }, 5000);
    };
    const formatPosition = (pos) => (typeof pos === 'number' ? pos.toFixed(1) : '0.0');

    const startCollectiveMotion = async () => {
      try {
        const motionData = { mode: collectiveMotion.value.mode };
        if (motionData.mode !== 'homing') {
          motionData.distance = collectiveMotion.value.distance;
        }
        await apiService.setBladeScrewsMotion(props.drumId, motionData);
        emit('motion-started', { drumId: props.drumId, type: 'collective', motionData });
        emit('success', `Collective blade motion started for Hopper ${props.drumId}`);
      } catch (error) {
        showError(error);
        emit('error', error);
      }
    };

    const cancelCollectiveMotion = async () => {
      try {
        await apiService.cancelBladeScrewsMotion(props.drumId);
        emit('motion-cancelled', { drumId: props.drumId, type: 'collective' });
        emit('success', `Collective blade motion cancelled for Hopper ${props.drumId}`);
      } catch (error) {
        showError(error);
        emit('error', error);
      }
    };

    const startIndividualMotion = async (screwId) => {
      try {
        await apiService.setBladeScrewMotion(props.drumId, screwId, { distance: individualMotion.value[screwId].distance });
        emit('success', `Motion started for Screw ${screwId} on Hopper ${props.drumId}`);
      } catch (error) {
        showError(error);
        emit('error', error);
      }
    };

    const cancelIndividualMotion = async (screwId) => {
      try {
        await apiService.cancelBladeScrewMotion(props.drumId, screwId);
        emit('success', `Motion cancelled for Screw ${screwId} on Hopper ${props.drumId}`);
      } catch (error) {
        showError(error);
        emit('error', error);
      }
    };

    return {
      collectiveMotion, individualMotion, isBladeRunning, getScrewRunning, formatPosition,
      errorMessage,
      startCollectiveMotion, cancelCollectiveMotion, startIndividualMotion, cancelIndividualMotion
    };
  }
};
</script>

<style scoped>
/* All styles are now inherited from the consistent card design */
.control-card { background: white; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.05); border: 1px solid #e5e7eb; transition: all 0.2s ease-in-out; display: flex; flex-direction: column; }
.control-card:hover { box-shadow: 0 4px 12px rgba(0,0,0,0.1); transform: translateY(-2px); }
.card-header { display: flex; justify-content: space-between; align-items: center; padding: 1rem 1.5rem; border-bottom: 1px solid #e5e7eb; }
.card-title { font-size: 1.1rem; font-weight: 600; color: #1f2937; }
.status-indicator { display: flex; align-items: center; gap: 0.5rem; }
.status-dot { width: 10px; height: 10px; border-radius: 50%; }
.status-running { background-color: #10b981; }
.status-stopped { background-color: #6b7280; }
.status-text { font-size: 0.875rem; color: #4b5563; font-weight: 500; }
.card-content { padding: 1.5rem; }
.info-grid { display: grid; grid-template-columns: 1fr 1fr; gap: 1rem; margin-bottom: 1.5rem; }
.info-box { background-color: #f9fafb; padding: 0.75rem; border-radius: 6px; border: 1px solid #f3f4f6; }
.info-label { display: block; font-size: 0.75rem; color: #6b7280; margin-bottom: 0.25rem; text-transform: uppercase; letter-spacing: 0.05em; }
.info-value { font-size: 1rem; font-weight: 500; color: #1f2937; }
.control-group { margin-bottom: 1.5rem; }
.control-group:last-child { margin-bottom: 0; }
.group-title { font-size: 1rem; font-weight: 600; color: #374151; margin-bottom: 1rem; }
.parameter-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(120px, 1fr)); gap: 1rem; margin-bottom: 1rem; }
.form-group { display: flex; flex-direction: column; }
.form-label { font-size: 0.875rem; color: #4b5563; margin-bottom: 0.25rem; }
.form-input, .form-select { width: 100%; padding: 0.5rem; border: 1px solid #d1d5db; border-radius: 6px; background-color: white; font-size: 0.875rem; }
.form-input:disabled, .form-select:disabled { background-color: #f3f4f6; cursor: not-allowed; }
.button-row { display: flex; gap: 0.5rem; }
.btn { flex: 1; padding: 0.6rem; border-radius: 6px; font-weight: 500; cursor: pointer; border: 1px solid transparent; transition: background-color 0.2s; }
.btn:disabled { opacity: 0.5; cursor: not-allowed; }
.btn-primary { background-color: #3b82f6; color: white; }
.btn-primary:hover:not(:disabled) { background-color: #2563eb; }
.btn-danger { background-color: #ef4444; color: white; }
.btn-danger:hover:not(:disabled) { background-color: #dc2626; }
.pressure-box { padding: 1rem; border-radius: 6px; border: 1px solid; }
.pressure-set { display: flex; gap: 0.5rem; align-items: center; }
.form-input-sm { flex-grow: 1; width: 50px; padding: 0.4rem; border: 1px solid #d1d5db; border-radius: 4px; font-size: 0.875rem; }
.btn-set { color: white; border: none; border-radius: 4px; padding: 0.4rem 0.8rem; cursor: pointer; font-weight: 500; transition: background-color 0.2s; }
.btn-set:disabled { cursor: not-allowed; }

/* LAYOUT FIX: Stack individual screw controls vertically */
.individual-screw-container {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}
</style>